<template>
	<farm-form id="operation-form" class="operation-form" v-model="isValidForm" ref="formRef">
		<farm-row>
			<farm-col
				id="product-selection-col"
				cols="12"
				class="mb-6"
				@click.native="openErrorModal(productsError && !isLoading)"
			>
				<SelectableProductCard :productName="productName" :is-active="!isWalletError.value">
					<template #right>
						<farm-tooltip v-if="hasJustOneProduct">
							O campo não permite alteração.
							<template #activator>
								<farm-btn
									icon
									aria-label="Ver mais"
									hover-color="primary"
									hover-color-variation="lighten"
									disabled
									@click="$emit('open-products-modal')"
								>
									<farm-icon size="20">pencil-outline</farm-icon>
								</farm-btn>
							</template>
						</farm-tooltip>
						<farm-btn
							v-else
							icon
							aria-label="Ver mais"
							hover-color="primary"
							hover-color-variation="lighten"
							:disabled="isWalletError.value"
							@click="$emit('open-products-modal')"
						>
							<farm-icon size="20">pencil-outline</farm-icon>
						</farm-btn>
					</template>
				</SelectableProductCard>
			</farm-col>
			<farm-col
				id="limit-selection-col"
				cols="12"
				class="mb-6"
				@click.native="openErrorModal(!hasOriginators)"
			>
				<SelectableCardLimit
					v-if="chosenOriginator"
					:captions="generateOriginatorCaption(chosenOriginator)"
					:item="chosenOriginator"
					is-active
				>
					<template #right>
						<farm-tooltip v-if="hasJustOneOriginator">
							O campo não permite alteração.
							<template #activator>
								<farm-btn
									icon
									aria-label="Ver mais"
									hover-color="primary"
									hover-color-variation="lighten"
									disabled
									@click="$emit('open-limits-modal')"
								>
									<farm-icon size="20">pencil-outline</farm-icon>
								</farm-btn>
							</template>
						</farm-tooltip>
						<farm-btn
							v-else
							icon
							aria-label="Ver mais"
							hover-color="primary"
							hover-color-variation="lighten"
							@click="$emit('open-limits-modal')"
						>
							<farm-icon size="20">pencil-outline</farm-icon>
						</farm-btn>
					</template>
				</SelectableCardLimit>
				<farm-btn
					v-else
					outlined
					class="operation-form__giant-button"
					:disabled="isLoading || !currentLimit"
					@click="hasOriginators && $emit('open-limits-modal')"
				>
					<div class="operation-form__button-content">
						Selecione o limite que deseja utilizar
						<farm-icon size="20" class="m-0">plus</farm-icon>
					</div>
				</farm-btn>
			</farm-col>
			<farm-col
				v-if="!hasJustOneCampaign"
				id="campaign-selection-col"
				cols="12"
				@click.native="openErrorModal(!hasCampaigns, campaignsError)"
			>
				<farm-label for="origination-machine-campaigns" required>
					Escolha uma campanha
				</farm-label>

				<farm-select
					v-model="form.campaignId"
					required
					id="origination-machine-campaigns"
					item-text="campaignAndCommercial_product"
					item-value="campaignId"
					:disabled="!currentLimit"
					:items="campaigns"
					:rules="currentLimit ? [rules.required] : []"
				/>
			</farm-col>
			<farm-col id="value-selection-col" cols="12">
				<farm-label for="origination-machine-value" required>
					Digite o valor desejado
				</farm-label>
				<farm-textfield-v2
					v-model="form.value"
					required
					id="origination-machine-value"
					:mask="CURRENCY_MASK"
					:disabled="!chosenOriginator"
					:rules="isValueTouched && chosenOriginator ? [
						rules.required,
						rules.higherThanZero,
						rules.hasExceededLimit,
						rules.minimumValue,
					] : []"
					@blur="isValueTouched = true"
				/>
			</farm-col>
			<farm-col
				id="date-selection-col"
				cols="12"

				@click.native="openErrorModal(!hasWorkingDays && Boolean(form.campaignId))"
				>
				<farm-label required for="supplier-transfer-date">
					Selecione a melhor data de transferência para o fornecedor
				</farm-label>
				<farm-select
					v-model="form.supplierTransferDate"
					required
					:disabled="!Boolean(form.campaignId)"
					id="supplier-transfer-date"
					:items="workingDays"
					:rules="[rules.required]"
					@change="fetchDueDates"
				/>
			</farm-col>
			<farm-col id="due-date-selection-col" cols="12">
				<farm-label required for="origination-machine-due-date">
					Selecione uma data de vencimento
				</farm-label>
				<farm-input-datepicker
					v-model="form.dueDate"
					required
					input-id="origination-machine-due-date"
					position="top"
					:disabled="!canEditDueDate"
					:readonly="!canEditDueDate"
					:min="(dueDates && dueDates.startDueDate) || ''"
					:max="(dueDates && dueDates.endDueDate) || ''"
					:allowed-dates="getAllowedDueDates"
					:picker-date="initialMonth"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col
				id="supplier-selection-col"
				cols="12"
				class="mb-6"

			>


				<SelectableCard
					v-if="chosenSupplier"
					class="mb-6"
					is-active
					has-top
					:captions="selectableCardCaptions"
					:item="chosenSupplier"
				>
					<template #top>
						<farm-subtitle
							class="d-flex align-center justify-space-between"
							color="primary"
							variation="medium"
							:type="2"
						>
							Selecione o fornecedor a ser pago
							<farm-tooltip v-if="hasJustOneSupplier">
								O campo não permite alteração.
								<template #activator>
									<farm-btn
										icon
										aria-label="Ver mais"
										hover-color="primary"
										hover-color-variation="lighten"
										disabled
										@click="$emit('open-supplier-modal')"
									>
										<farm-icon size="20">pencil-outline</farm-icon>
									</farm-btn>
								</template>
							</farm-tooltip>
							<farm-btn
								v-else
								icon
								aria-label="Ver mais"
								hover-color="primary"
								hover-color-variation="lighten"
								@click="$emit('open-supplier-modal')"
							>
								<farm-icon size="20">pencil-outline</farm-icon>
							</farm-btn>
						</farm-subtitle>

						<farm-line />
					</template>
				</SelectableCard>

				<SelectableCard
					v-if="chosenSupplier"
					is-active
					has-top
					:captions="accountInfos"
					:item="chosenSupplier"
				>
					<template #top>
						<farm-subtitle
							class="d-flex align-center justify-space-between"
							color="primary"
							variation="medium"
							:type="2"
						>
							Dados bancários do fornecedor

							<farm-tooltip v-if="hasJustOneBankAccounts">
								O campo não permite alteração.
								<template #activator>
									<farm-btn
										icon
										aria-label="Ver mais"
										hover-color="primary"
										hover-color-variation="lighten"
										disabled
										@click="$emit('open-bank-account-modal')"
									>
										<farm-icon size="20">pencil-outline</farm-icon>
									</farm-btn>
								</template>
							</farm-tooltip>
							<farm-btn
								v-else
								icon
								aria-label="Ver mais"
								hover-color="primary"
								hover-color-variation="lighten"
								@click="$emit('open-bank-account-modal')"
							>
								<farm-icon size="20">pencil-outline</farm-icon>
							</farm-btn>
						</farm-subtitle>

						<farm-line />
					</template>
				</SelectableCard>

				<farm-btn
					v-else
					outlined
					class="operation-form__giant-button"
					:disabled="!canEnableSupplierSelection"
					@click="handleSupplierSelection"
				>
					<div class="operation-form__button-content">
						Selecione o fornecedor a ser pago
						<farm-icon size="20" class="mr-0">plus</farm-icon>
					</div>
				</farm-btn>
			</farm-col>

		</farm-row>
		<WorkingCapitalErrorModal
			v-model="isOpenErrorModal"
			:error-messages="campaignsError || []"
			@on-close="onCloseErrorModal"
		/>
		<WorkingCapitalDueDateErrorModal
			v-model="isOpenDueDateErrorModal"
			@on-close="onCloseDueDateErrorModal"
		/>
		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-form>
</template>

<script lang="ts">
import { computed, defineComponent, ref, watch } from 'vue';

import { brlPlain } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { useModal, useRoute, useSelectedProductId } from '@/composible';
import SelectableCard from '@/features/workingCapitalV2/components/SelectableCard';
import SelectableCardLimit from '@/features/workingCapitalV2/components/SelectableCardLimit';
import SelectableProductCard from '@/features/workingCapitalV2/components/SelectableProductCard';
import WorkingCapitalDueDateErrorModal from '@/features/workingCapitalV2/components/WorkingCapitalDueDateErrorModal';
import WorkingCapitalErrorModal from '@/features/workingCapitalV2/components/WorkingCapitalErrorModal';
import { yourLimitsGetter } from '@/features/workingCapitalV2/components/YourLimits/composition';
import {
	useCampaigns,
	useOriginatorLimit,
	useSuppliers,
	useWorkingCapitalCreateForm,
} from '@/features/workingCapitalV2/composables';
import { key, query } from '@/features/workingCapitalV2/configurations/queries';
import { handleDueDatesError } from '@/features/workingCapitalV2/configurations/queries/workingCapital';
import { CURRENCY_MASK } from '@/features/workingCapitalV2/constants';
import { handlePreEligibilityError } from '@/features/workingCapitalV2/helpers/handlePreEligibilityError';
import { priceWorkingCapital } from '@/features/workingCapitalV2/services';
import {
	PriceWorkingCapitalRequest,
	PriceWorkingCapitalResponse,
} from '@/features/workingCapitalV2/services/types';
import { OperationTypeEnum } from '@/types';

export default defineComponent({
	name: 'operation-form',
	components: {
		SelectableCard,
		SelectableCardLimit,
		SelectableProductCard,
		WorkingCapitalDueDateErrorModal,
		WorkingCapitalErrorModal,
	},
	props: {
		operationTypeId: {
			type: Number,
			required: true,
		},
	},
	setup(_, { emit }) {
		const route = useRoute();
		const { wallet, currentLimit, isWalletError }: any = yourLimitsGetter();
		const productId = useSelectedProductId().value;

		const canEditDueDate = ref(false);
		const dueDates = ref(null);
		const formRef = ref(null);
		const productsError = ref(false);
		const hasJustOneProduct = ref(false);
		const {
			form,
			isValidForm,
			totalToPay,
			rawFormValue,
			rules,
			chosenOriginator,
			totalLimit,
			generateOriginatorCaption,
			resetChosenOriginator,
			isValueValid,
			simulationId,
			setSimulationId,
		} = useWorkingCapitalCreateForm(formRef);

		const {
			suppliersByCommercialProductQuery: { isFetching: isFetchingSuppliers },
			chosenSupplier,
			hasSuppliers,
			selectableCardCaptions,
			removeChosenSupplier,
			selectableBankAccount,
			accountInfos,
			hasJustOneSupplier,
			hasJustOneBankAccounts,
			suppliersError,
		} = useSuppliers();

		const {
			workingCapitalCampaignsQuery: { data: campaigns, isFetching: isFetchingCampaigns },
			chosenCampaign,
			hasCampaigns,
			commercialProductId,
			campaignCommercialProductId,
			hasCommercialProduct,
			hasJustOneCampaign,
			campaignsError,
			refetchCampaigns
		} = useCampaigns();

		const {
			originatorsLimitsByCommercialProductQuery: { isFetching: isFetchingOriginators },
				hasOriginators,
				hasJustOneOriginator,
				refetchOriginators
		} = useOriginatorLimit();

		const {
			isOpenModal: isOpenErrorModal,
			onOpenModal: onOpenErrorModal,
			onCloseModal: onCloseErrorModal,
		} = useModal();

		const {
			isOpenModal: isOpenDueDateErrorModal,
			onOpenModal: onOpenDueDateErrorModal,
			onCloseModal: onCloseDueDateErrorModal,
		} = useModal();

		const isValueTouched = ref(false);

		const superCessionWorkingDaysRequest = computed(() => ({
			params: {
				productId: productId,
				operationTypeId: route.path.includes('credito-ponte')
					? OperationTypeEnum.BRIDGE
					: OperationTypeEnum.ORIGINATION,
				campaignCommercialProductId: campaignCommercialProductId.value,
			},
		}));

		const workingCapitalDueDatesRequest = computed(() => ({
			params: {
				campaignCommercialProductId: campaignCommercialProductId.value,
			},
			query: {
				disbursement_date: form.value.supplierTransferDate,
			},
			operationId: route.path.includes('credito-ponte')
				? OperationTypeEnum.BRIDGE
				: OperationTypeEnum.ORIGINATION,
		}));

		const priceWorkingCapitalRequest: ComputedRef<PriceWorkingCapitalRequest> = computed(
			() => ({
				params: {
					productId: productId,
					operationTypeId: route.path.includes('credito-ponte')
						? OperationTypeEnum.BRIDGE
						: OperationTypeEnum.ORIGINATION,
				},
				payload: {
					operationId: null,
					campaignId: chosenCampaign.value.campaignId,
					commercialProductId: currentLimit.value?.id,
					campaignCommercialProductId: chosenCampaign.value?.Id,
					draweeId: form.value.originatorId,
					draweeDocument: chosenOriginator.value?.document,
					availableLimit: totalLimit.value,
					requestedValue: rawFormValue.value,
					paymentValue: 0,
					dueDate: form.value.dueDate,
					disbursementDate: form.value.supplierTransferDate,
					receivableId: null,
					vehicleId: null,
					uuid: null,
				},
			} as PriceWorkingCapitalRequest)
		);

		const { data: workingDays, refetch: getWorkingDays } = query.superCession.workingDays({
			queryKey: key.superCession.workingDays(commercialProductId),
			request: superCessionWorkingDaysRequest,
			onError: (errorMessages) => {
				emit('open-error-modal', errorMessages);
			},
			options: {
				enabled: hasCommercialProduct,
				cacheTime: 0,
			},
		});

		const handleDueDatesQueryError = (errorMessages: string[]) => {
			canEditDueDate.value = false;
			emit('open-error-modal', errorMessages);
		};

		const { refetch: getDueDates } = query.workingCapital.dueDates({
			queryKey: key.workingCapital.dueDates(campaignCommercialProductId),
			request: workingCapitalDueDatesRequest,
			callback: (response) => {
				// Clear any previous errors on success
				// This callback is called from the query configuration on successful response
			},
			onError: handleDueDatesQueryError,
			options: {
				enabled: false,
				cacheTime: 0,
				onSuccess: data => {
					dueDates.value = data;
					canEditDueDate.value = true;
					if (
						data?.errors.length > 0 &&
						data?.errors[0] ===
							'Não há data de vencimento disponível para a data de desembolso informada'
					) {
						canEditDueDate.value = false;
						onOpenDueDateErrorModal(true);
						return;
					}
				},
				onError: (error) => {
					// This onError is for TanStack Query internal errors
					// The actual API error handling is now done in the query configuration
					canEditDueDate.value = false;
				},
			},
		});

		const { isLoading: isLoadingPricing, mutate } = useMutation({
			mutationFn: () => priceWorkingCapital(priceWorkingCapitalRequest.value),
			onSuccess: (data: PriceWorkingCapitalResponse) => {
				totalToPay.value = data.data.pricedValue;
				simulationId.value = data.data.simulationId;
				setSimulationId(data.data.simulationId);
			},
			onError: (error) => {
				if (error?.response?.status === 400 && error?.response?.data?.errors) {
					const errorMessages = handlePreEligibilityError(error);
					openErrorModal(true, errorMessages);
				} else {
					openErrorModal(true);
				}
			},
		});

		const isLoading = computed(
			() => isFetchingCampaigns.value || isLoadingPricing.value || isFetchingOriginators.value
		);

		const hasWorkingDays = computed(() => !!workingDays.value.length);

		const hasDueDates = computed(
			() => dueDates.value?.startDueDate && dueDates.value?.endDueDate
		);

		const initialMonth = computed(() => {
			if (hasDueDates.value) {
				return dueDates.value?.startDueDate.substring(0, 7);
			}
			return new Date().toISOString().substring(0, 7);
		});

		const productName = computed(() => {
			return currentLimit.value?.name ?? '';
		});

		function getAllowedDueDates(date) {
			if (!hasDueDates.value) {
				return false;
			}
			return !dueDates.value.holidays.includes(date) || false;
		}

		const openErrorModal = (condition, errorMessages = []) => {
			if (condition) {
				if (errorMessages.length > 0) {
					emit('open-error-modal', errorMessages);
				} else {
					onOpenErrorModal();
					emit('has-no-info');
				}
			}
		};

		const fetchDueDates = () => {
			canEditDueDate.value = false;
			form.value.dueDate = null;
			getDueDates();
		};

		const handleSupplierSelection = async () => {
			try {
				if (canEnableSupplierSelection.value) {
					totalToPay.value = null;
					simulationId.value = null;
					await mutate();
					emit('open-supplier-modal');
				}
			} catch (error) {
				openErrorModal(true);
			}
		};

		watch(wallet, newValue => {
			if (newValue) {
				if (newValue.length === 0) {
					productsError.value = true;
					return;
				}
				if (newValue.length === 1) {
					hasJustOneProduct.value = true;
				}
				productsError.value = false;
			} else {
				productsError.value = true;
			}
		});

		watch(currentLimit, () => {
			form.value.campaignId = null;
			form.value.value = null;
			isValueTouched.value = false;
			resetChosenOriginator();
			removeChosenSupplier();
			if (currentLimit.value) {
				refetchCampaigns();
				refetchOriginators();
			}
		});

		watch(campaigns, (newCampaigns) => {
			if (hasJustOneCampaign.value && newCampaigns?.length === 1) {
				form.value.campaignId = newCampaigns[0].campaignId;
			}
		}, { immediate: true });

		watch(chosenOriginator, () => {
			if (chosenSupplier.value) {
				removeChosenSupplier();
			}
			isValueTouched.value = false;
		});

		watch(
			() => form.value.value,
			() => {
				if (chosenSupplier.value) {
					removeChosenSupplier();
				}
			}
		);

		watch(
			() => form.value.campaignId,
			() => {
				if (chosenSupplier.value) {
					removeChosenSupplier();
				}
					form.value.supplierTransferDate = null;
					form.value.dueDate = null;
			}
		);

		watch(
			() => form.value.supplierTransferDate,
			() => {
				if (form.value.supplierTransferDate === null) {
					form.value.dueDate = null;
					canEditDueDate.value = false;
					if (chosenSupplier.value) {
						removeChosenSupplier();
					}
				}
			}
		);

		watch(
			() => form.value.dueDate,
			() => {
				if (chosenSupplier.value) {
					removeChosenSupplier();
				}
			}
		);

		watch(chosenOriginator, () => {
			if (chosenSupplier.value) {
				removeChosenSupplier();
			}
		});

		watch(commercialProductId, () => {
			form.value.dueDate = null;
			form.value.supplierTransferDate = null;
			removeChosenSupplier();
		});

		watch(chosenCampaign, (newValue) => {
			if (newValue && Object.keys(newValue).length > 0) {
				getWorkingDays();
			}
		}, { deep: true });

		watch(campaignsError, (newError) => {
			if (newError && newError.length > 0) {
				openErrorModal(true, newError);
			}
		});

		watch(suppliersError, (newError) => {
			if (newError && newError.length > 0) {
				openErrorModal(true, newError);
			}
		});

		const canEnableSupplierSelection = computed(() => {
			const conditions = {
				hasValue: Boolean(form.value.value),
				hasOriginator: Boolean(chosenOriginator.value),
				hasCampaign: Boolean(form.value.campaignId),
				hasTransferDate: Boolean(form.value.supplierTransferDate),
				hasDueDate: Boolean(form.value.dueDate),
				isValueValid: isValueValid.value,
				isNotLoading: !isLoading.value
			};

			return Object.values(conditions).every(condition => condition === true);
		});

		return {
			CURRENCY_MASK,
			form,
			isValidForm,
			isFetchingSuppliers,
			isLoading,
			rules,
			campaigns,
			hasCampaigns,
			hasOriginators,
			chosenOriginator,
			hasSuppliers,
			hasDueDates,
			workingDays,
			hasWorkingDays,
			selectableCardCaptions,
			dueDates,
			chosenSupplier,
			removeChosenSupplier,
			getAllowedDueDates,
			brlPlain,
			openErrorModal,
			isOpenErrorModal,
			onOpenErrorModal,
			onCloseErrorModal,
			isOpenDueDateErrorModal,
			onOpenDueDateErrorModal,
			onCloseDueDateErrorModal,
			generateOriginatorCaption,
			hasJustOneOriginator,
			fetchDueDates,
			canEditDueDate,
			initialMonth,
			selectableBankAccount,
			accountInfos,
			hasJustOneSupplier,
			hasJustOneBankAccounts,
			hasJustOneCampaign,
			campaignsError,
			formRef,
			productName,
			hasJustOneProduct,
			productsError,
			currentLimit,
			isWalletError,
			isValueValid,
			canEnableSupplierSelection,
			handleSupplierSelection,
			simulationId,
			setSimulationId,
			isValueTouched,
		};
	},
});
</script>

<style lang="scss" scoped>
@import 'OperationForm';
</style>
