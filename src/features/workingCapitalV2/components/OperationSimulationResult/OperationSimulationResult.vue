<template>
	<farm-col cols="12" md="4" class="operation-simulation-result d-flex flex-column pa-6">
		<farm-bodytext variation="bold" color="primary" class="mb-4">
			Resultado da simulação
		</farm-bodytext>

		<WorkingCapitalStepper :steps="steps" />
		<farm-line class="my-4" color="primary" />

		<dl class="d-flex flex-column">
			<!-- Condicional para exibir Campanha apenas para Working Capital -->
			<div v-if="isWorkingCapital" class="d-flex justify-space-between mb-6">
				<farm-bodytext type="2" variation="regular" color="primary" tag="dt">
					Campanha
				</farm-bodytext>
				<farm-bodytext type="2" variation="regular" tag="dd">
					{{
						(chosenCampaign &&
							chosenCampaign.campaignAndCommercial_product.split('-')[0]) ||
						'-'
					}}
				</farm-bodytext>
			</div>

			<div class="d-flex justify-space-between mb-6">
				<farm-bodytext type="2" variation="regular" color="primary" tag="dt">
					Taxa
				</farm-bodytext>
				<farm-bodytext type="2" variation="regular" tag="dd">
					{{ (chosenCampaign && chosenCampaign.tax && `${chosenCampaign.tax}%`) || '-' }}
				</farm-bodytext>
			</div>

			<div class="d-flex justify-space-between mb-6">
				<farm-bodytext type="2" variation="regular" color="primary" tag="dt">
					Valor desejado
				</farm-bodytext>
				<farm-bodytext type="2" variation="regular" tag="dd">
					{{ form.value ? `R$ ${form.value}` : '-' }}
				</farm-bodytext>
			</div>

			<!-- Exibir Vencimento apenas para Working Capital -->
			<div v-if="isWorkingCapital" class="d-flex justify-space-between">
				<farm-bodytext type="2" variation="regular" color="primary" tag="dt">
					Vencimento
				</farm-bodytext>
				<farm-bodytext type="2" variation="regular" tag="dd">
					{{ form.dueDate ? defaultDateFormat(form.dueDate) : '-' }}
				</farm-bodytext>
			</div>
		</dl>

		<farm-line class="my-6" color="primary" />

		<div class="d-flex justify-space-between mb-10">
			<farm-bodytext type="2" variation="regular" color="primary">
				{{ totalToPayLabel }}
			</farm-bodytext>
			<farm-bodytext type="2" variation="bold">
				{{ brl(totalToPay) || '-' }}
			</farm-bodytext>
		</div>

		<div class="d-flex justify-end mt-auto">
			<farm-btn outlined class="mr-2" @click="onOpenCancelModal"> Cancelar</farm-btn>
			<farm-btn :disabled="!canAdvance" @click="executePreEligibility">Contratar</farm-btn>
		</div>

		<WorkingCapitalErrorModal
			v-if="isOpenErrorModal"
			v-model="isOpenErrorModal"
			@on-close="onCloseErrorModal"
		>
			<template #disclaimer>
				<ul v-for="(error, index) in errorsArr" :key="index">
					<farm-bodytext type="2" variation="regular" tag="li">
						{{ error }}
					</farm-bodytext>
				</ul>
			</template>
		</WorkingCapitalErrorModal>
		<WorkingCapitalSuccessModal
			v-if="isOpenSuccessModal"
			v-model="isOpenSuccessModal"
			:dates="cessionOperationAmortizationDates"
			:isWorkingCapital="isWorkingCapital"
			@on-close="onCloseSuccessModal"
			@create="createPreEligibility"
		/>
		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-col>
</template>
<script lang="ts">
import { defineComponent, ref, computed, getCurrentInstance, defineAsyncComponent} from 'vue';

import { defaultDateFormat, brl } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';


import { useModal, useRouter, useSelectedProductId } from '@/composible';
import WorkingCapitalStepper from '@/features/workingCapitalV2/components/WorkingCapitalStepper';
import { useCampaigns, useWorkingCapitalCreateForm } from '@/features/workingCapitalV2/composables';
import { handleGenericError } from '@/features/workingCapitalV2/helpers/handleGenericError';
import { handlePreEligibilityError } from '@/features/workingCapitalV2/helpers/handlePreEligibilityError';
import { simulatePreEligibility, createPreEligibility as createPreEligibilityService } from '@/features/workingCapitalV2/services';
import { SimulatePreEligibilityResponse } from '@/features/workingCapitalV2/services/types';
import { OperationTypeEnum } from '@/types';

export default defineComponent({
	components: {
		WorkingCapitalStepper,
		WorkingCapitalErrorModal: defineAsyncComponent(
			() => import('@/features/workingCapitalV2/components/WorkingCapitalErrorModal')
		),
		WorkingCapitalSuccessModal: defineAsyncComponent(
			() => import('@/features/workingCapitalV2/components/WorkingCapitalSuccessModal')
		),
	},
	props: {
		operationTypeId: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const errorsArr = ref([]);
		const internalInstance = getCurrentInstance().proxy;
		const { chosenCampaign } = useCampaigns();
		const productId = useSelectedProductId().value;
		const {
			form,
			isValidForm,
			totalToPay,
			canAdvance,
			hasPreEligibilityApproved,
			openCancelModal,
			simulationId,
			setSimulationId,
		} = useWorkingCapitalCreateForm();
		const {
			isOpenModal: isOpenErrorModal,
			onOpenModal: onOpenErrorModal,
			onCloseModal: onCloseErrorModal,
		} = useModal();
		const router = useRouter();

		const {
			isOpenModal: isOpenSuccessModal,
			onOpenModal: onOpenSuccessModal,
			onCloseModal: onCloseSuccessModal,
		} = useModal();

		const preEligibilityId = ref(null);
		const preEligibilityUuid = ref(null);
		const preEligibilityVehicleId = ref(null);

		const isWorkingCapital = computed(() => props.operationTypeId === OperationTypeEnum.BRIDGE);

		const hasCessionDates = computed(() =>
			Boolean(
				chosenCampaign.value?.startCessionExpiration &&
					chosenCampaign.value?.endCessionExpiration
			)
		);

		const cessionOperationAmortizationDates = computed(
			() =>
				`${defaultDateFormat(
					chosenCampaign.value?.startCessionExpiration
				)} até ${defaultDateFormat(chosenCampaign.value?.endCessionExpiration)}`
		);

		const steps = computed(() => {
			const baseSteps = [
				{
					icon: 'currency-usd',
					label: 'Transferência para o Fornecedor',
					value: defaultDateFormat(form.value.supplierTransferDate),
				},
				{
					icon: 'calendar-blank',
					label: isWorkingCapital.value ? 'Vencimento Ponte' : 'Vencimento',
					value: defaultDateFormat(form.value.dueDate),
				},
			];
			if (isWorkingCapital.value) {
				baseSteps.push({
					icon: 'calendar-range',
					label: 'Vencimento Recebíveis para Amortização',
					value: hasCessionDates.value ? cessionOperationAmortizationDates.value : null,
				});
			}
			return baseSteps;
		});

		const totalToPayLabel = computed(() =>
			isWorkingCapital.value ? 'Total a pagar em recebíveis' : 'Total a Pagar'
		);

		const workingCapitalPreEligibilityRequest = computed(() => ({
			params: {
				productId: productId,
			},
			payload: {
				providers: [
					{
						providerId: form.value.supplierId,
						bankDataId: form.value.supplierBankAccount,
					},
				],
				simulationId: simulationId.value,
			}
		}));
		const workingCapitalCreatePreEligibilityRequest = computed(() => ({
			params: {
				productId: productId,
				operationId: props.operationTypeId,
			},
			payload: { simulationId: simulationId.value },
		}));

		/**
		 * Detects the type of error codes in the error response and routes to the appropriate handler
		 * @param {any} error - The error object from the API response
		 * @returns {string[]} - Array of processed error messages
		 */
		const handleSimulatePreEligibilityError = (error: any): string[] => {
			const errorMessages = error?.response?.data?.errors || [];

			if (errorMessages.length === 0) {
				return handlePreEligibilityError(error);
			}

			// Check for numeric codes (legacy format) - pattern: (123)
			const numericCodeRegex = /\(\d+\)$/;
			const hasNumericCodes = errorMessages.some((msg: string) => numericCodeRegex.test(msg));

			// Check for alphanumeric codes (new format) - pattern: (CM4), (ABC123)
			const alphanumericCodeRegex = /\([A-Za-z0-9]+\)$/;
			const hasAlphanumericCodes = errorMessages.some((msg: string) => alphanumericCodeRegex.test(msg));

			// Priority logic: if both types exist, prioritize alphanumeric (newer format)
			if (hasAlphanumericCodes) {
				return handleGenericError(error);
			} else if (hasNumericCodes) {
				return handlePreEligibilityError(error);
			} else {
				// No codes found, use legacy handler for fallback behavior
				return handlePreEligibilityError(error);
			}
		};

		const { isLoading: isLoadingPricing, mutate: executePreEligibility } = useMutation({
			mutationFn: () =>
				simulatePreEligibility(
					workingCapitalPreEligibilityRequest.value,
					props.operationTypeId
				),
			onSuccess: (response: SimulatePreEligibilityResponse) => {
				hasPreEligibilityApproved.value = response.data.data.approved ?? false;

				if (!hasPreEligibilityApproved.value) {
					onOpenErrorModal();

					return;
				}

				onOpenSuccessModal();

				preEligibilityId.value = response.data.data.id || null;
				preEligibilityUuid.value = response.data.data.uuid || null;
				preEligibilityVehicleId.value = response.data.data.vehicleId || null;
			},
			onError: (error: any) => {
				errorsArr.value = handleSimulatePreEligibilityError(error);
				onOpenErrorModal();
			},
		});

		const { isLoading: isLoadingCreate, mutate: createPreEligibility } = useMutation({
			mutationFn: () => {
				onCloseSuccessModal();
				return createPreEligibilityService(
					workingCapitalCreatePreEligibilityRequest.value,
				);
			},
			onSuccess: () => {
				internalInstance.$dialog
					.alert(
						{
							title: 'Sucesso',
							body: `<p>Sua operação foi criada com sucesso!</p><br/><p>Para acompanhar o status e as alterações acesse os detalhes através da listagem de operações ${
								isWorkingCapital.value ? 'capital de giro' : 'máquina de originação'
							}.</p>`,
						},
						{
							html: true,
							okText: 'Ok',
						}
					)
					.then(() => {
						router.push(
							`/admin/wallet/${
								isWorkingCapital.value ? 'credito-ponte' : 'maquina-originacao'
							}?from=create`
						);
					})
					.catch(() => {});
			},
			onError: onOpenErrorModal,
		});

		function onOpenCancelModal() {
			return openCancelModal(internalInstance);
		}

		const isLoading = computed(() => isLoadingPricing.value || isLoadingCreate.value);

		return {
			errorsArr,
			form,
			steps,
			isValidForm,
			totalToPay,
			chosenCampaign,
			hasCessionDates,
			canAdvance,
			cessionOperationAmortizationDates,
			workingCapitalPreEligibilityRequest,
			isLoadingPricing,
			isOpenErrorModal,
			onCloseErrorModal,
			executePreEligibility,
			defaultDateFormat,
			brl,
			onOpenCancelModal,
			isOpenSuccessModal,
			onCloseSuccessModal,
			onOpenSuccessModal,
			createPreEligibility,
			isLoading,
			isWorkingCapital,
			totalToPayLabel,
			simulationId,
			setSimulationId,
		};
	},
});
</script>
<style lang="scss" scoped>
@import 'OperationSimulationResult';
</style>
