import { ref, computed } from 'vue';

import { useRoute, useSelectedProductId } from '@/composible';
import { yourLimitsGetter } from '@/features/workingCapitalV2/components/YourLimits/composition';
import {  useWorkingCapitalCreateForm } from '@/features/workingCapitalV2/composables';
import { key, query } from '@/features/workingCapitalV2/configurations/queries';
import { GetSuppliersBankAccountsResponse } from '@/features/workingCapitalV2/services/types';
import { OperationTypeEnum } from '@/types';


const chosenSupplier = ref(null);
const filters = ref({
	term: '',
});
const pagination = ref({
	page: 0,
	limit: 10,
});
const metaPagination = ref({
	totalPages: 1,
});
const sorting = ref({
	orderby: 'name',
	order: 'ASC',
});
const bankAccounts = ref([]);
const accountInfos = ref([]);
const suppliers = ref([]);
const suppliersError = ref(null);
const bankAccountsError = ref(null);

export default function useSuppliers() {
	const route = useRoute();
	const { form, simulationId } = useWorkingCapitalCreateForm();
	const { currentLimit }: any = yourLimitsGetter();
	const productId = useSelectedProductId().value;
	const hasChosenSupplier = computed(() => Boolean(chosenSupplier.value));
	const supplierDocument = computed(() => chosenSupplier.value?.document);
	const suppliersListRequest = computed(() => {
		if (!simulationId?.value) {
			return {
				query: {
					page: pagination.value.page,
					limit: pagination.value.limit,
					orderby: sorting.value.orderby,
					order: sorting.value.order,
					...(filters.value.term !== null || filters.value.term !== undefined
						? { search: filters.value.term }
						: {}),
				}
			};
		}

		return {
			params: {
				simulationId: simulationId.value
			},
			query: {
				page: pagination.value.page,
				limit: pagination.value.limit,
				orderby: sorting.value.orderby,
				order: sorting.value.order,
				...(filters.value.term !== null || filters.value.term !== undefined
					? { search: filters.value.term }
					: {}),
			}
		};
	});
	const suppliersBankAccountsRequest = computed(() => ({
		params: {
			document: chosenSupplier.value?.document,
		},
	}));

	const updateChosenSupplier = supplier => {
		chosenSupplier.value = supplier;

		form.value.supplierId = supplier?.id;
	};
	const removeChosenSupplier = () => {
		chosenSupplier.value = null;
		form.value.supplierId = null;
		removeChosenBankAccount();
	};
	const updateChosenBankAccount = bankAccount => {
		form.value.supplierBankAccount = bankAccount;
		makeAccountInfos();
	};
	const removeChosenBankAccount = () => {
		form.value.supplierBankAccount = null;
	};
	const updateSupplierSearchTerm = (term: string) => {
		filters.value.term = term;

	};

	const resetSupplier = () => {
		removeChosenSupplier();
	};
	const resetSupplierPagination = () => {
		pagination.value.page = 0;
		pagination.value.limit = 10;

	};

	const handleSuppliersError = (errorMessages: string[]) => {
		suppliersError.value = errorMessages;
	};

	const handleBankAccountsError = (errorMessages: string[]) => {
		bankAccountsError.value = errorMessages;
	};

	const makeAccountInfos = () => {
		accountInfos.value = [];
		if (!form.value.supplierBankAccount) return;

		const account = bankAccounts.value?.find(
			bankAccount => bankAccount.id === form.value.supplierBankAccount
		);

		if (account.type === 'account') {
			accountInfos.value.push(
				{
					caption: 'Tipo',
					value: `Conta ${account.accountType}`,
				},

				{
					caption: 'Banco',
					value: account.bank,
				},
				{
					caption: 'Agência',
					value: account.agency,
				},
				{
					caption: 'Número da Conta',
					value: account.account,
				}
			);
			return;
		}
		accountInfos.value.push(
			{
				caption: 'Tipo',
				value: 'PIX',
			},
			{
				caption: 'Tipo de Chave',
				value: account.pixKeyType,
			},
			{
				caption: 'Chave',
				value: account.pixKey,
			}
		);
	};

	const suppliersByCommercialProductQuery = query.suppliers.byCommercialProduct({
		queryKey: key.suppliers.byCommercialProduct(
			simulationId || ref(null),
			pagination,
			sorting,
			filters
		),
		request: suppliersListRequest,
		callback: response => {
			suppliersError.value = null;
			metaPagination.value.totalPages = response.data.totalPages;
			suppliers.value = response.data.content || [];
			if (response.data.content?.length === 1) {
				updateChosenSupplier(response.data.content[0]);
			}
		},
		onError: handleSuppliersError,
		options: {
			enabled: computed(() => !!simulationId?.value),
			cacheTime: 0,
			retry: 0,
		},
	});

	const suppliersBankAccountsQuery = query.suppliers.bankAccounts({
		queryKey: key.suppliers.bankAccounts(supplierDocument),
			request: suppliersBankAccountsRequest,
			callback: (response: GetSuppliersBankAccountsResponse) => {
				bankAccountsError.value = null;
				form.value.supplierBankAccount = response.data.content[0]?.id || null;
				bankAccounts.value = response.data.content || [];
				makeAccountInfos();
			},
			onError: handleBankAccountsError,
			options: {
				enabled: hasChosenSupplier,
				cacheTime: 0,
			},
	});

	const hasJustOneSupplier = computed(() => {
		return suppliersByCommercialProductQuery.data.value?.content?.length === 1;
	});

	const hasJustOneBankAccounts = computed(() => bankAccounts.value?.length === 1);

	const selectableCardCaptions = computed(() => [
		{
			caption: 'Razão Social',
			value: chosenSupplier.value?.name,
		},
		{
			caption: 'CNPJ',
			value: chosenSupplier.value?.document,
		},
	]);
	const selectableBankAccount = computed(() => {
		return accountInfos.value;
	});

	const hasSuppliers = computed(() => !!suppliers.value.length);

	const isSupplierDataReady = computed(() => {
		return !suppliersByCommercialProductQuery.isLoading.value &&
			!suppliersByCommercialProductQuery.isError.value &&
			suppliers.value.length > 0;
	});

	return {
		pagination,
		metaPagination,
		sorting,
		suppliersByCommercialProductQuery,
		suppliersBankAccountsQuery,
		chosenSupplier,
		suppliers,
		hasSuppliers,
		bankAccounts,
		supplierDocument,
		hasChosenSupplier,
		selectableCardCaptions,
		suppliersBankAccountsRequest,
		suppliersListRequest,
		resetSupplier,
		resetSupplierPagination,
		updateSupplierSearchTerm,
		updateChosenSupplier,
		removeChosenSupplier,
		updateChosenBankAccount,
		removeChosenBankAccount,
		selectableBankAccount,
		accountInfos,
		hasJustOneSupplier,
		hasJustOneBankAccounts,
		isSupplierDataReady,
		suppliersError,
		bankAccountsError,
	};
}
