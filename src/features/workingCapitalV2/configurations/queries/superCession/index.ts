import { useQuery } from '@tanstack/vue-query';
import type { UseQueryOptions } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { builderWorkingDays } from '@/features/workingCapitalV2/helpers/builderWorkingDays';
import { ERROR_MESSAGES } from '@/features/workingCapitalV2/constants/errorMessages';
import { handleGenericError } from '@/features/workingCapitalV2/helpers/handleGenericError';
import { handlePreEligibilityError } from '@/features/workingCapitalV2/helpers/handlePreEligibilityError';
import { getWorkingDays } from '@/features/workingCapitalV2/services';
import type {
	GetWorkingDaysRequest,
	GetWorkingDaysResponse,
} from '@/features/workingCapitalV2/services/types';

export const handleWorkingDaysError = (error: any): string[] => {
	if (error?.response?.status === 400 && error?.response?.data?.errors) {
		return handleGenericError(error);
	}
	return [ERROR_MESSAGES.GENERIC_NOT_FOUND];
};

export default {
	workingDays({
		queryKey,
		request,
		options,
		callback,
		onError,
	}: {
		queryKey: any[];
		request?: ComputedRef<GetWorkingDaysRequest>;
		options?: Partial<UseQueryOptions>;
		callback?: (data: any) => void;
		onError?: (errorMessages: string[]) => void;
	}) {
		return useQuery({
			queryKey,
			queryFn: async () => {
				try {
					const response = await getWorkingDays(request.value);
					if (callback) callback(response);

					return response;
				} catch (error) {
					if (onError) {
						const errorMessages = handleWorkingDaysError(error);
						onError(errorMessages);
					}

					throw error;
				}
			},
			select: (response: GetWorkingDaysResponse) => {
				const rawWorkingDays = response.data?.data?.content || [];

				return builderWorkingDays(rawWorkingDays);
			},
			refetchOnWindowFocus: false,
			retry: 0,
			placeholderData: {
				data: {
					data: {
						content: [],
					},
				},
			},
			...(options as any),
		});
	},
};
