import { UseQueryOptions, useQuery } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { ERROR_MESSAGES } from '@/features/workingCapitalV2/constants/errorMessages';
import { handleGenericError } from '@/features/workingCapitalV2/helpers/handleGenericError';
import { handlePreEligibilityError } from '@/features/workingCapitalV2/helpers/handlePreEligibilityError';
import { getDueDates, getWorkingCapitalCampaignsList } from '@/features/workingCapitalV2/services';
import type {
	GetDueDatesRequest,
	GetDueDatesResponse,
	GetWorkingCapitalCampaignsListRequest,
	GetWorkingCapitalCampaignsListResponse,
} from '@/features/workingCapitalV2/services/types';

export const handleDueDatesError = (error: any): string[] => {
	if (error?.response?.status === 400 && error?.response?.data?.errors) {
		return handleGenericError(error);
	}
	return [ERROR_MESSAGES.GENERIC_NOT_FOUND];
};

export const handleCampaignsError = (error: any): string[] => {
	if (error?.response?.status === 400 && error?.response?.data?.errors) {
		return handleGenericError(error);
	}
	return [ERROR_MESSAGES.GENERIC_NOT_FOUND];
};

export default {
	campaigns({
		queryKey,
		request,
		options,
		callback,
		onError,
	}: {
		queryKey: any[];
		request: ComputedRef<GetWorkingCapitalCampaignsListRequest>;
		options?: Partial<UseQueryOptions>;
		callback?: (data: any) => void;
		onError?: (errorMessages: string[]) => void;
	}) {
		return useQuery({
			queryKey,
			queryFn: async () => {
				try {
					const response = await getWorkingCapitalCampaignsList(request.value);
					if (callback) callback(response);

					return response;
				} catch (error) {
					if (onError) {
						const errorMessages = handleCampaignsError(error);
						onError(errorMessages);
					}

					throw error;
				}
			},
			select: (queryData: GetWorkingCapitalCampaignsListResponse) =>
				queryData.data?.data?.content || ({} as typeof queryData.data.data.content),
			refetchOnWindowFocus: false,
			retry: 0,
			...(options as any),
		});
	},
	dueDates({
		queryKey,
		request,
		options,
		callback,
		onError,
	}: {
		queryKey: any[];
		request: ComputedRef<GetDueDatesRequest>;
		options?: Partial<UseQueryOptions>;
		callback?: (data: any) => void;
		onError?: (errorMessages: string[]) => void;
	}) {
		return useQuery({
			queryKey,
			queryFn: async () => {
				try {
					const response = await getDueDates(request.value, request.value.operationId);

					if (callback) callback(response);

					return response;
				} catch (error) {
					if (onError) {
						const errorMessages = handleDueDatesError(error);
						onError(errorMessages);
					}

					throw error;
				}
			},
			select: (queryData: GetDueDatesResponse) =>
				queryData.data || ({} as typeof queryData.data),
			refetchOnWindowFocus: false,
			retry: 0,
			placeholderData: {
				startDueDate: null,
				endDueDate: null,
				holidays: [],
			},
			...(options as any),
		});
	},
};
