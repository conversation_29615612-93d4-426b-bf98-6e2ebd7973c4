import { ERROR_MESSAGES } from '@/features/workingCapitalV2/constants/errorMessages';

export function handlePreEligibilityError(error: any) {
    const errorMessages = error?.response?.data?.errors || [ERROR_MESSAGES.UNEXPECTED_ERROR];
    const regex = /\([A-Za-z0-9]+\)$/;

    const isSecondScenario = errorMessages.some((msg: string) => regex.test(msg));
    if (isSecondScenario) {
        return errorMessages;
    }

    return errorMessages.map((msg: string) => {
        if (/Segmento|Segmentação/i.test(msg)) {
            return "Problema no segmento/classificação.";
        }
        if (/Fornecedor/i.test(msg)) {
            return "Problema no fornecedor.";
        }
        return ERROR_MESSAGES.UNEXPECTED_ERROR;
    });
}
