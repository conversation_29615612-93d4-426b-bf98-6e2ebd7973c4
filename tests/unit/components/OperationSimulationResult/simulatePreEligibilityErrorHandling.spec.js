import { handleGenericError } from '../../../../src/features/workingCapitalV2/helpers/handleGenericError';
import { handlePreEligibilityError } from '../../../../src/features/workingCapitalV2/helpers/handlePreEligibilityError';

// Mock the handlers
jest.mock('../../../../src/features/workingCapitalV2/helpers/handleGenericError');
jest.mock('../../../../src/features/workingCapitalV2/helpers/handlePreEligibilityError');

describe('simulatePreEligibility Error Handling Logic', () => {
    // Simulate the logic from handleSimulatePreEligibilityError function
    const handleSimulatePreEligibilityError = (error) => {
        const errorMessages = error?.response?.data?.errors || [];
        
        if (errorMessages.length === 0) {
            return handlePreEligibilityError(error);
        }

        // Check for numeric codes (legacy format) - pattern: (123)
        const numericCodeRegex = /\(\d+\)$/;
        const hasNumericCodes = errorMessages.some((msg) => numericCodeRegex.test(msg));

        // Check for alphanumeric codes (new format) - pattern: (CM4), (ABC123)
        const alphanumericCodeRegex = /\([A-Za-z0-9]+\)$/;
        const hasAlphanumericCodes = errorMessages.some((msg) => alphanumericCodeRegex.test(msg));

        // Priority logic: if both types exist, prioritize alphanumeric (newer format)
        if (hasAlphanumericCodes) {
            return handleGenericError(error);
        } else if (hasNumericCodes) {
            return handlePreEligibilityError(error);
        } else {
            // No codes found, use legacy handler for fallback behavior
            return handlePreEligibilityError(error);
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        handleGenericError.mockReturnValue(['Generic error handled']);
        handlePreEligibilityError.mockReturnValue(['PreEligibility error handled']);
    });

    it('should use handleGenericError for alphanumeric codes', () => {
        const error = {
            response: {
                data: {
                    errors: ['Não há data de vencimento disponível para a data de desembolso informada (CM4)']
                }
            }
        };

        const result = handleSimulatePreEligibilityError(error);

        expect(handleGenericError).toHaveBeenCalledWith(error);
        expect(handlePreEligibilityError).not.toHaveBeenCalled();
        expect(result).toEqual(['Generic error handled']);
    });

    it('should use handlePreEligibilityError for numeric codes', () => {
        const error = {
            response: {
                data: {
                    errors: ['Erro no segmento (123)']
                }
            }
        };

        const result = handleSimulatePreEligibilityError(error);

        expect(handlePreEligibilityError).toHaveBeenCalledWith(error);
        expect(handleGenericError).not.toHaveBeenCalled();
        expect(result).toEqual(['PreEligibility error handled']);
    });

    it('should prioritize alphanumeric codes when both types are present', () => {
        const error = {
            response: {
                data: {
                    errors: [
                        'Erro no segmento (123)',
                        'Não há data de vencimento disponível (CM4)'
                    ]
                }
            }
        };

        const result = handleSimulatePreEligibilityError(error);

        expect(handleGenericError).toHaveBeenCalledWith(error);
        expect(handlePreEligibilityError).not.toHaveBeenCalled();
        expect(result).toEqual(['Generic error handled']);
    });

    it('should use handlePreEligibilityError when no codes are found', () => {
        const error = {
            response: {
                data: {
                    errors: ['Erro sem código específico']
                }
            }
        };

        const result = handleSimulatePreEligibilityError(error);

        expect(handlePreEligibilityError).toHaveBeenCalledWith(error);
        expect(handleGenericError).not.toHaveBeenCalled();
        expect(result).toEqual(['PreEligibility error handled']);
    });

    it('should use handlePreEligibilityError when no errors array is present', () => {
        const error = {
            response: {
                data: {}
            }
        };

        const result = handleSimulatePreEligibilityError(error);

        expect(handlePreEligibilityError).toHaveBeenCalledWith(error);
        expect(handleGenericError).not.toHaveBeenCalled();
        expect(result).toEqual(['PreEligibility error handled']);
    });

    it('should handle complex alphanumeric codes correctly', () => {
        const error = {
            response: {
                data: {
                    errors: [
                        'Fornecedor não encontrado (F001)',
                        'Documento CNPJ inválido (D003)',
                        'Limite insuficiente (L007)'
                    ]
                }
            }
        };

        const result = handleSimulatePreEligibilityError(error);

        expect(handleGenericError).toHaveBeenCalledWith(error);
        expect(handlePreEligibilityError).not.toHaveBeenCalled();
        expect(result).toEqual(['Generic error handled']);
    });
});
