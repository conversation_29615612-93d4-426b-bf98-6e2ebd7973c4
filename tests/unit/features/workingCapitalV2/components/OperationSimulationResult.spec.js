import { shallowMount, createLocalVue } from '@vue/test-utils';
import Vuex from 'vuex';

import OperationSimulationResult from '@/features/workingCapitalV2/components/OperationSimulationResult';

jest.mock('@tanstack/vue-query', () => ({
	useMutation: () => ({
		isLoading: false,
		isError: false,
		mutate: jest.fn(),
	}),
	useQuery: () => ({
		data: {},
		isLoading: false,
		isFetching: false,
		isError: false,
	}),
}));

jest.mock('@/composible', () => ({
	useModal: () => ({
		isOpenModal: false,
		onOpenModal: jest.fn(),
		onCloseModal: jest.fn(),
	}),
	useRouter: () => ({
		push: jest.fn(),
	}),
	useSelectedProductId: () => ({
		value: 1,
	}),
}));

jest.mock('@/features/workingCapitalV2/composables', () => ({
	useCampaigns: () => ({
		chosenCampaign: {
			value: {
				campaignAndCommercial_product: 'test-campaign',
				tax: 10,
				startCessionExpiration: '2024-01-01',
				endCessionExpiration: '2024-12-31',
			},
		},
	}),
	useWorkingCapitalCreateForm: () => ({
		form: {
			value: {
				supplierId: 1,
				supplierBankAccount: 1,
				supplierTransferDate: '2024-01-01',
				dueDate: '2024-12-31',
			},
		},
		isValidForm: true,
		totalToPay: 1000,
		canAdvance: true,
		hasPreEligibilityApproved: false,
		openCancelModal: jest.fn(),
		simulationId: { value: '123' },
		setSimulationId: jest.fn(),
	}),
}));

const localVue = createLocalVue();
localVue.use(Vuex);

let store = new Vuex.Store({
	modules: {
		dashboards: {
			namespaced: true,
			getters: {
				yourLimits: () => [],
				yourLimitsRequestStatus: () => 'IDLE',
				currentLimit: () => {},
			},
			actions: {
				selectCurrentLimit: jest.fn(),
				cleanCurrentLimit: jest.fn(),
				fetchYourLimits: jest.fn(),
			},
		},
	},
});

describe('OperationSimulationResult component', () => {
	let wrapper;

	beforeEach(() => {
		wrapper = shallowMount(OperationSimulationResult, {
			localVue,
			store,
			propsData: {
				operationTypeId: 2,
			},
		});
	});

	test('OperationSimulationResult created', () => {
		expect(wrapper).toBeDefined();
	});
});
