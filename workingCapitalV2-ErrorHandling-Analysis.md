# Working Capital V2 - Análise Técnica de Tratamento de Erros

## Visão Geral

Esta análise documenta o tratamento atual de erros na feature Working Capital V2, focando especificamente nos três endpoints que receberão melhorias no backend: `getDueDates`, `getSuppliersList` e `getSuppliersBankAccounts`.

## Arquitetura Atual de Tratamento de Erros

### 1. Estrutura de Serviços

Os três serviços principais estão localizados em `src/features/workingCapitalV2/services/services.ts`:

<augment_code_snippet path="src/features/workingCapitalV2/services/services.ts" mode="EXCERPT">
````typescript
export const getDueDates = (request: GetDueDatesRequest, operationId: number) =>
	financingClient.get<GetDueDatesResponse>(
		`/workingCapital/${request.params.campaignCommercialProductId}/operation/${operationId}/dueDate`,
		{
			params: request.query,
		}
	);

export const getSuppliersList = (
	request: GetProvidersListRequest
): Promise<GetProvidersListResponse> =>
	financingClient.get(
		`/workingCapital/${request.params.simulationId}/providers`,
		{
			params: request.query,
		}
	);

export const getSuppliersBankAccounts = (
	request: GetSuppliersBankAccountsRequest
): Promise<GetSuppliersBankAccountsResponse> =>
	superCessionClient.get(`/api/v1/operation/0/providers/${request.params.document}/bank-data`, {
		params: request.query,
	});
````
</augment_code_snippet>

### 2. Configurações de Query (TanStack Query)

#### getDueDates
- **Localização**: `src/features/workingCapitalV2/configurations/queries/workingCapital/index.ts`
- **Configuração**: 
  - `enabled: false` (execução manual via refetch)
  - `cacheTime: 0` (sem cache)
  - Tratamento específico de erros de negócio via `onSuccess`
  - Fallback genérico via `onError`

<augment_code_snippet path="src/features/workingCapitalV2/configurations/queries/workingCapital/index.ts" mode="EXCERPT">
````typescript
dueDates({
	queryKey,
	request,
	options,
	callback,
}: {
	queryKey: any[];
	request: ComputedRef<GetDueDatesRequest>;
	options?: Partial<UseQueryOptions>;
	callback?: (data: any) => void;
}) {
	return useQuery({
		queryKey,
		queryFn: async () => {
			const response = await getDueDates(request.value, request.value.operationId);

			if (callback) callback(response);

			return response;
		},
		select: (queryData: GetDueDatesResponse) =>
			queryData.data || ({} as typeof queryData.data),
		refetchOnWindowFocus: false,
		placeholderData: {
			startDueDate: null,
			endDueDate: null,
			holidays: [],
		},
		...(options as any),
	});
},
````
</augment_code_snippet>

#### getSuppliersList e getSuppliersBankAccounts
- **Localização**: `src/features/workingCapitalV2/configurations/queries/suppliers/index.ts`
- **Configuração**:
  - `retry: 0` (sem retry automático)
  - `cacheTime: 0` para suppliers, `staleTime: 60000` para bankAccounts
  - Tratamento de erro via try/catch com fallback para dados vazios

<augment_code_snippet path="src/features/workingCapitalV2/configurations/queries/suppliers/index.ts" mode="EXCERPT">
````typescript
byCommercialProduct({
	queryKey,
	request,
	options,
	callback,
}: {
	queryKey: any[];
	request?: ComputedRef<GetProvidersListRequest>;
	options?: Partial<UseQueryOptions>;
	callback?: (data: any) => void;
}) {
	return useQuery({
		queryKey,
		queryFn: async () => {
			try {
				const response = await getSuppliersList(request.value);

				if (callback) callback(response);

				return response;
			} catch (error) {
				if (callback)
					callback({
						data: {
							content: [],
							page: 0,
							size: 10,
							totalPages: 0,
							totalItems: 0,
						},
					});

				return {
					data: {
						content: [],
						page: 0,
						size: 10,
						totalPages: 0,
						totalItems: 0,
					},
				};
			}
		},
		select: (response: GetProvidersListResponse) => response.data,
		keepPreviousData: true,
		refetchOnWindowFocus: false,
		retry: 0,
		staleTime: 60 * 1000,
		...(options as any),
	});
},
````
</augment_code_snippet>

### 3. Tratamento de Erros nos Composables

#### useSuppliers
- **Localização**: `src/features/workingCapitalV2/composables/useSuppliers/useSuppliers.ts`
- **Estratégia**: Fallback silencioso para dados vazios
- **Estado de erro**: Verificado via `suppliersByCommercialProductQuery.isError.value`

#### Uso no OperationForm
- **Localização**: `src/features/workingCapitalV2/components/OperationForm/OperationForm.vue`
- **getDueDates**: Tratamento específico com dois tipos de modal de erro:
  - `WorkingCapitalDueDateErrorModal`: Para erro específico de datas indisponíveis
  - `WorkingCapitalErrorModal`: Para erros genéricos

<augment_code_snippet path="src/features/workingCapitalV2/components/OperationForm/OperationForm.vue" mode="EXCERPT">
````typescript
const { refetch: getDueDates } = query.workingCapital.dueDates({
	queryKey: key.workingCapital.dueDates(campaignCommercialProductId),
	request: workingCapitalDueDatesRequest,
	options: {
		enabled: false,
		cacheTime: 0,
		onSuccess: data => {
			dueDates.value = data;
			canEditDueDate.value = true;
			if (
				data?.errors.length > 0 &&
				data?.errors[0] ===
					'Não há data de vencimento disponível para a data de desembolso informada'
			) {
				canEditDueDate.value = false;
				onOpenDueDateErrorModal(true);
				return;
			}
		},
		onError: () => {
			canEditDueDate.value = false;
			openErrorModal(true);
		},
	},
});
````
</augment_code_snippet>

### 4. Componentes de UI para Exibição de Erros

#### WorkingCapitalErrorModal
- **Localização**: `src/features/workingCapitalV2/components/WorkingCapitalErrorModal/WorkingCapitalErrorModal.vue`
- **Funcionalidade**: Modal genérico para erros
- **Conteúdo**: Mensagem fixa + informações de contato
- **Limitação**: Não suporta mensagens dinâmicas ou listas de erros

<augment_code_snippet path="src/features/workingCapitalV2/components/WorkingCapitalErrorModal/WorkingCapitalErrorModal.vue" mode="EXCERPT">
````vue
<template #content>
	<section class="mb-4">
		<slot name="disclaimer">
			<farm-bodytext type="2" variation="regular" class="mb-4">
				Não foi possível encontrar nenhuma informação cadastrada para seleção.
			</farm-bodytext>
		</slot>
		<farm-subtitle type="2" variation="regular" class="mt-4">
			Em caso de dúvidas ou problemas, entre em contato através do chat ou canais de
			atendimento:
		</farm-subtitle>
	</section>
````
</augment_code_snippet>

#### WorkingCapitalDueDateErrorModal
- **Localização**: `src/features/workingCapitalV2/components/WorkingCapitalDueDateErrorModal/WorkingCapitalDueDateErrorModal.vue`
- **Funcionalidade**: Modal específico para erros de data de vencimento
- **Conteúdo**: Mensagem específica + informações de contato

### 5. Helper de Tratamento de Erros Existente

#### handlePreEligibilityError
- **Localização**: `src/features/workingCapitalV2/helpers/handlePreEligibilityError/handlePreEligibilityError.ts`
- **Funcionalidade**: Processa arrays de mensagens de erro
- **Lógica**: 
  - Detecta mensagens com códigos `(número)` e as retorna sem modificação
  - Aplica mapeamento para mensagens específicas (Segmento, Fornecedor)
  - Fallback para mensagem genérica

<augment_code_snippet path="src/features/workingCapitalV2/helpers/handlePreEligibilityError/handlePreEligibilityError.ts" mode="EXCERPT">
````typescript
export function handlePreEligibilityError(error: any) {
    const defaultErrorMessage = "Um erro inesperado ocorreu";
    const errorMessages = error?.response?.data?.errors || [defaultErrorMessage];
    const regex = /\(\d+\)$/;

    const isSecondScenario = errorMessages.some((msg: string) => regex.test(msg));
    if (isSecondScenario) {
        return errorMessages;
    }

    return errorMessages.map((msg: string) => {
        if (/Segmento|Segmentação/i.test(msg)) {
            return "Problema no segmento/classificação.";
        }
        if (/Fornecedor/i.test(msg)) {
            return "Problema no fornecedor.";
        }
        return defaultErrorMessage;
    });
}
````
</augment_code_snippet>

## Problemas Identificados

### 1. Tratamento Inconsistente
- **getDueDates**: Usa callbacks onSuccess/onError
- **getSuppliersList/getSuppliersBankAccounts**: Usa try/catch com fallback silencioso
- **Falta de padronização** no tratamento de erros HTTP 400

### 2. Limitações dos Modais
- **WorkingCapitalErrorModal**: Mensagem fixa, não suporta listas de erros
- **Ausência de modal específico** para erros de suppliers e bankAccounts
- **Não há integração** com o helper handlePreEligibilityError existente

### 3. Perda de Informações de Erro
- **Fallback silencioso** nos suppliers queries oculta erros importantes
- **Não há logging** ou notificação de erros para debugging
- **Mensagens genéricas** não ajudam o usuário a entender o problema

## Formato de Erro Esperado (Backend)

```json
{
  "meta": null,
  "errors": [
    "mensagem de erro específica (código)",
    "outra mensagem de erro (código2)"
  ]
}
```

## Modificações Necessárias

### 1. Atualização dos Query Configurations
- Implementar tratamento consistente de erros HTTP 400
- Integrar com handlePreEligibilityError helper
- Manter fallback para dados vazios quando apropriado

### 2. Melhoria dos Modais de Erro
- Tornar WorkingCapitalErrorModal dinâmico para aceitar listas de mensagens
- Criar modal específico para erros de suppliers se necessário
- Implementar slot para mensagens customizadas

### 3. Padronização do Fluxo de Erro
- Definir estratégia consistente para todos os três endpoints
- Implementar logging adequado para debugging
- Manter UX adequada com fallbacks apropriados

### 4. Testes
- Criar testes para cenários de erro HTTP 400
- Validar exibição correta de listas de mensagens
- Testar fallbacks e recuperação de erro

## Fluxo Detalhado de Chamadas de API

### 1. getDueDates - Fluxo Completo

```mermaid
sequenceDiagram
    participant User
    participant OperationForm
    participant Query
    participant Service
    participant Backend
    participant Modal

    User->>OperationForm: Seleciona campanha/data
    OperationForm->>Query: refetch getDueDates
    Query->>Service: getDueDates(request, operationId)
    Service->>Backend: GET /workingCapital/{id}/operation/{id}/dueDate

    alt Success Response
        Backend-->>Service: 200 + data
        Service-->>Query: response.data
        Query-->>OperationForm: onSuccess(data)
        OperationForm->>OperationForm: dueDates.value = data

        alt Business Error in Response
            OperationForm->>OperationForm: Check data.errors
            OperationForm->>Modal: onOpenDueDateErrorModal(true)
        end
    else HTTP Error (400/500/etc)
        Backend-->>Service: Error Response
        Service-->>Query: throw error
        Query-->>OperationForm: onError()
        OperationForm->>Modal: openErrorModal(true)
    end
```

**Pontos Críticos**:
- Erro HTTP vai para `onError` → `WorkingCapitalErrorModal`
- Erro de negócio (response.data.errors) vai para `onSuccess` → `WorkingCapitalDueDateErrorModal`
- **Problema**: Erros HTTP 400 estruturados não são processados adequadamente

### 2. getSuppliersList - Fluxo Completo

```mermaid
sequenceDiagram
    participant User
    participant useSuppliers
    participant Query
    participant Service
    participant Backend

    User->>useSuppliers: Trigger via simulationId change
    useSuppliers->>Query: suppliersByCommercialProductQuery
    Query->>Service: getSuppliersList(request)
    Service->>Backend: GET /workingCapital/{simulationId}/providers

    alt Success Response
        Backend-->>Service: 200 + data
        Service-->>Query: response
        Query-->>useSuppliers: callback(response)
        useSuppliers->>useSuppliers: suppliers.value = response.data.content

        alt Auto-selection Logic
            useSuppliers->>useSuppliers: updateChosenSupplier(single supplier)
        end
    else HTTP Error
        Backend-->>Service: Error Response
        Service-->>Query: catch(error)
        Query-->>useSuppliers: callback(empty data)
        useSuppliers->>useSuppliers: suppliers.value = []
        Note over useSuppliers: Silent fallback - no user notification
    end
```

**Pontos Críticos**:
- **Fallback silencioso**: Erros são mascarados com dados vazios
- **Sem notificação ao usuário**: Não há feedback visual de erro
- **Perda de contexto**: Mensagens de erro específicas são perdidas

### 3. getSuppliersBankAccounts - Fluxo Completo

```mermaid
sequenceDiagram
    participant User
    participant useSuppliers
    participant Query
    participant Service
    participant Backend

    User->>useSuppliers: updateChosenSupplier(supplier)
    useSuppliers->>Query: suppliersBankAccountsQuery (enabled by hasChosenSupplier)
    Query->>Service: getSuppliersBankAccounts(request)
    Service->>Backend: GET /api/v1/operation/0/providers/{document}/bank-data

    alt Success Response
        Backend-->>Service: 200 + data
        Service-->>Query: response
        Query-->>useSuppliers: callback(response)
        useSuppliers->>useSuppliers: bankAccounts.value = response.data.content
        useSuppliers->>useSuppliers: Auto-select first account
    else HTTP Error
        Backend-->>Service: Error Response
        Service-->>Query: catch(error)
        Query-->>useSuppliers: callback(empty data)
        useSuppliers->>useSuppliers: bankAccounts.value = []
        Note over useSuppliers: Silent fallback - no user notification
    end
```

**Pontos Críticos**:
- **Mesmo padrão de fallback silencioso** que getSuppliersList
- **Dependência de supplier selecionado**: Query só executa se hasChosenSupplier = true
- **Auto-seleção da primeira conta**: Pode mascarar problemas de dados

## Estados de Erro e Recuperação

### 1. Estados Atuais por Endpoint

| Endpoint | Estado Loading | Estado Error | Fallback | Notificação | Modal |
|----------|----------------|--------------|----------|-------------|-------|
| **getDueDates** | ❌ (manual) | ✅ isError | ❌ | ❌ | ✅ Específico |
| **getSuppliersList** | ✅ isLoading | ✅ isError | ✅ Dados vazios | ❌ | ❌ |
| **getSuppliersBankAccounts** | ✅ isLoading | ✅ isError | ✅ Dados vazios | ❌ | ❌ |

### 2. Estratégias de Recuperação Atuais

#### getDueDates
```typescript
// Estratégia: Modal de erro + desabilitação de campo
onError: () => {
    canEditDueDate.value = false;
    openErrorModal(true);
}
```

#### getSuppliersList/getSuppliersBankAccounts
```typescript
// Estratégia: Fallback silencioso
catch (error) {
    if (callback) callback({ data: { content: [] } });
    return { data: { content: [] } };
}
```

### 3. Problemas de UX Identificados

1. **Inconsistência**: Diferentes estratégias para endpoints similares
2. **Falta de contexto**: Usuário não sabe por que não há suppliers/contas
3. **Debugging difícil**: Erros silenciosos dificultam troubleshooting
4. **Recuperação limitada**: Não há opção de retry para suppliers/bankAccounts

## Integração com Sistema de Notificações

### 1. Padrão Atual (outros composables)
```typescript
// Exemplo de outros composables na feature
onError: err => {
    const error = errorBuilder(err);
    notification(
        RequestStatusEnum.ERROR,
        `Erro ao buscar os dados: ${error?.message || ''}`
    );
}
```

### 2. errorBuilder da Biblioteca
- **Função**: Extrai mensagem de erro de diferentes formatos de response
- **Localização**: `@farm-investimentos/front-mfe-libs-ts`
- **Limitação**: Não está sendo usado nos três endpoints analisados

### 3. Sistema de Notificação
- **Função**: `notification(status, message)`
- **Tipos**: SUCCESS, ERROR, WARNING, INFO
- **Localização**: Toasts no topo da aplicação
- **Vantagem**: Não bloqueia interface como modais

## Mapeamento de Dependências

### 1. Dependências Diretas
```
OperationCreate.vue
├── OperationForm.vue (usa getDueDates)
├── useSuppliers (usa getSuppliersList + getSuppliersBankAccounts)
├── WorkingCapitalErrorModal
└── WorkingCapitalDueDateErrorModal
```

### 2. Dependências Indiretas
```
useSuppliers
├── useWorkingCapitalCreateForm (form state)
├── query.suppliers.byCommercialProduct
├── query.suppliers.bankAccounts
└── key.suppliers.* (query keys)
```

### 3. Componentes Afetados por Mudanças
- **OperationForm.vue**: Precisa tratar novos formatos de erro
- **WorkingCapitalErrorModal**: Precisa suportar listas de mensagens
- **useSuppliers**: Precisa estratégia de erro mais robusta
- **Query configurations**: Precisam tratamento consistente de HTTP 400

## Considerações de Performance

### 1. Configurações de Cache Atuais
- **getDueDates**: `cacheTime: 0` (sem cache)
- **getSuppliersList**: `cacheTime: 0, retry: 1`
- **getSuppliersBankAccounts**: `staleTime: 60000, retry: 0`

### 2. Impacto das Mudanças
- **Tratamento de erro não deve afetar performance**
- **Cache deve ser mantido para bankAccounts** (dados mais estáveis)
- **Retry pode ser problemático** se erros 400 forem persistentes

### 3. Recomendações
- **Manter configurações de cache existentes**
- **Implementar retry inteligente** (não para erros 400)
- **Considerar debounce** para chamadas frequentes

## Plano de Implementação Recomendado

### Fase 1: Padronização do Tratamento de Erros

#### 1.1 Criar Helper Unificado
```typescript
// src/features/workingCapitalV2/helpers/handleApiError/handleApiError.ts
export function handleApiError(error: any, context: string) {
    // Verificar se é erro HTTP 400 com formato estruturado
    if (error?.response?.status === 400 && error?.response?.data?.errors) {
        return handlePreEligibilityError(error);
    }

    // Usar errorBuilder para outros tipos de erro
    const processedError = errorBuilder(error);

    // Log para debugging
    console.error(`[${context}] API Error:`, error);

    return [processedError?.message || 'Erro inesperado ocorreu'];
}
```

#### 1.2 Atualizar Query Configurations
```typescript
// Para getDueDates - manter lógica atual mas adicionar tratamento 400
onError: (error) => {
    const errorMessages = handleApiError(error, 'getDueDates');
    // Decidir entre modal específico ou genérico baseado no tipo de erro
    if (error?.response?.status === 400) {
        // Usar modal genérico com mensagens dinâmicas
        openErrorModalWithMessages(errorMessages);
    } else {
        // Manter comportamento atual
        openErrorModal(true);
    }
}

// Para suppliers - substituir try/catch silencioso
queryFn: async () => {
    try {
        const response = await getSuppliersList(request.value);
        if (callback) callback(response);
        return response;
    } catch (error) {
        const errorMessages = handleApiError(error, 'getSuppliersList');

        // Notificar usuário sobre erro
        notification(RequestStatusEnum.ERROR, errorMessages[0]);

        // Manter fallback para dados vazios
        const emptyResponse = { data: { content: [], totalPages: 0 } };
        if (callback) callback(emptyResponse);
        return emptyResponse;
    }
}
```

### Fase 2: Melhoria dos Componentes de UI

#### 2.1 Tornar WorkingCapitalErrorModal Dinâmico
```vue
<!-- WorkingCapitalErrorModal.vue -->
<template>
  <farm-modal>
    <template #content>
      <section class="mb-4">
        <slot name="disclaimer">
          <div v-if="errorMessages && errorMessages.length > 0">
            <farm-bodytext
              v-for="(message, index) in errorMessages"
              :key="index"
              type="2"
              variation="regular"
              class="mb-2"
            >
              {{ message }}
            </farm-bodytext>
          </div>
          <farm-bodytext v-else type="2" variation="regular" class="mb-4">
            Não foi possível encontrar nenhuma informação cadastrada para seleção.
          </farm-bodytext>
        </slot>
        <!-- Resto do conteúdo... -->
      </section>
    </template>
  </farm-modal>
</template>

<script>
export default {
  props: {
    value: { type: Boolean, required: true },
    errorMessages: { type: Array, default: () => [] }
  }
}
</script>
```

#### 2.2 Atualizar OperationCreate para Passar Mensagens
```typescript
// OperationCreate.vue
const errorMessages = ref([]);

const onError = (messages = []) => {
    errorMessages.value = messages;
    onOpenErrorModal();
};

// Template
<WorkingCapitalErrorModal
  v-model="isOpenErrorModal"
  :error-messages="errorMessages"
  @on-close="onCloseErrorModal"
/>
```

### Fase 3: Implementação de Estados de Erro Específicos

#### 3.1 Adicionar Estados no useSuppliers
```typescript
// useSuppliers.ts
const suppliersError = ref(null);
const bankAccountsError = ref(null);

const isSupplierDataReady = computed(() => {
    return !suppliersByCommercialProductQuery.isLoading.value &&
           !suppliersByCommercialProductQuery.isError.value &&
           suppliers.value.length > 0 &&
           !suppliersError.value;
});

const hasSuppliersError = computed(() => !!suppliersError.value);
const hasBankAccountsError = computed(() => !!bankAccountsError.value);
```

#### 3.2 Feedback Visual para Estados de Erro
```vue
<!-- OperationForm.vue -->
<div v-if="hasSuppliersError" class="error-state">
  <farm-alert type="error" class="mb-4">
    <div v-for="message in suppliersError" :key="message">
      {{ message }}
    </div>
  </farm-alert>
  <farm-btn outlined @click="retrySuppliers">
    Tentar novamente
  </farm-btn>
</div>
```

### Fase 4: Testes e Validação

#### 4.1 Testes Unitários
```typescript
// handleApiError.spec.ts
describe('handleApiError', () => {
  it('should handle HTTP 400 with structured errors', () => {
    const error = {
      response: {
        status: 400,
        data: {
          errors: ['Fornecedor inválido (123)', 'Documento não encontrado (456)']
        }
      }
    };

    const result = handleApiError(error, 'test');
    expect(result).toEqual(['Fornecedor inválido (123)', 'Documento não encontrado (456)']);
  });

  it('should fallback to errorBuilder for other errors', () => {
    const error = { response: { status: 500 } };
    const result = handleApiError(error, 'test');
    expect(result).toHaveLength(1);
  });
});
```

#### 4.2 Testes de Integração
```typescript
// OperationForm.spec.ts
describe('OperationForm Error Handling', () => {
  it('should display error modal with multiple messages for HTTP 400', async () => {
    // Mock API response
    mockGetDueDates.mockRejectedValue({
      response: {
        status: 400,
        data: { errors: ['Error 1 (123)', 'Error 2 (456)'] }
      }
    });

    // Trigger getDueDates
    await wrapper.vm.handleDateSelection();

    // Verify modal is shown with correct messages
    expect(wrapper.findComponent(WorkingCapitalErrorModal).props('errorMessages'))
      .toEqual(['Error 1 (123)', 'Error 2 (456)']);
  });
});
```

## Checklist de Implementação

### ✅ Análise Completa
- [x] Mapeamento de arquitetura atual
- [x] Identificação de problemas
- [x] Documentação de fluxos de erro
- [x] Análise de dependências

### 🔄 Próximos Passos
- [ ] Implementar helper unificado `handleApiError`
- [ ] Atualizar query configurations para tratamento consistente
- [ ] Tornar `WorkingCapitalErrorModal` dinâmico
- [ ] Adicionar estados de erro específicos em `useSuppliers`
- [ ] Implementar feedback visual para erros
- [ ] Criar testes unitários e de integração
- [ ] Validar UX com diferentes cenários de erro
- [ ] Documentar padrões para futuras implementações

## Considerações Finais

### Impacto na UX
- **Melhoria significativa** na transparência de erros
- **Feedback mais específico** para problemas de suppliers/bankAccounts
- **Consistência** no tratamento de erros em toda a feature

### Impacto Técnico
- **Código mais maintível** com tratamento padronizado
- **Debugging facilitado** com logs estruturados
- **Extensibilidade** para futuros endpoints com formato similar

### Riscos Mitigados
- **Fallback silencioso** substituído por feedback adequado
- **Perda de contexto** eliminada com mensagens específicas
- **Inconsistência** resolvida com padrão unificado
