# Working Capital V2 - Implementação do Sistema de Mensagens de Erro

## 📋 Visão Geral

Este documento explica as melhorias implementadas no sistema de tratamento de erros da feature **Working Capital V2**, focando em proporcionar mensagens mais claras e específicas para os usuários quando algo dá errado durante a criação de operações.

## 🎯 Problema Anterior

Antes das melhorias, quando ocorriam erros nas chamadas de API, os usuários recebiam:

- ❌ **Mensagens genéricas**: "Não foi possível encontrar nenhuma informação cadastrada para seleção"
- ❌ **Fallback silencioso**: Erros eram mascarados com dados vazios
- ❌ **Comportamento inconsistente**: Diferentes estratégias para endpoints similares
- ❌ **Falta de contexto**: Usuário não sabia por que não havia dados disponíveis

## ✨ Solução Implementada

Agora, quando o backend retorna erros estruturados (HTTP 400), o sistema:

- ✅ **Exibe mensagens específicas** enviadas pelo backend
- ✅ **Mostra múltiplas mensagens** quando há vários problemas
- ✅ **Mantém consistência** no tratamento de erros
- ✅ **Fornece contexto claro** sobre o que aconteceu

### Exemplo de Melhoria

**Antes:**
```
"Não foi possível encontrar nenhuma informação cadastrada para seleção."
```

**Depois:**
```
• Fornecedor não encontrado para este segmento (F001)
• Documento CNPJ inválido para operação (D003)
• Limite insuficiente para valor solicitado (L007)
```

---

## 🔧 Mudanças Implementadas

### ✅ APIs Migradas para o Novo Sistema

As seguintes APIs agora suportam mensagens de erro detalhadas:

#### 1. **Campaigns** 📊
- **Endpoint**: `GET /workingCapital/{productId}/operation/{operationId}/campaigns`
- **Erro típico**: Campanhas indisponíveis para o produto/segmento
- **Implementação**: Query configuration com `handleCampaignsError`

#### 2. **Suppliers** 🏭
- **Endpoint**: `GET /workingCapital/{simulationId}/providers`  
- **Erro típico**: Fornecedores não elegíveis para a simulação
- **Implementação**: Query configuration com `handleSuppliersListError`

#### 3. **Bank Accounts** 🏦
- **Endpoint**: `GET /api/v1/operation/0/providers/{document}/bank-data`
- **Erro típico**: Contas bancárias inválidas ou inexistentes
- **Implementação**: Query configuration com `handleSuppliersBankAccountsError`

#### 4. **Working Days** 📅
- **Endpoint**: `GET /receivables/orders/v1/products/{productId}/supersection/workingdays`
- **Erro típico**: Dias úteis indisponíveis para transferência
- **Implementação**: Query configuration com `handleWorkingDaysError`

#### 5. **Due Dates** 📆
- **Endpoint**: `GET /workingCapital/{id}/operation/{id}/dueDate`
- **Erro típico**: Datas de vencimento indisponíveis
- **Implementação**: Melhorado com `handleDueDatesError`

### ✅ Componentes Melhorados

#### 1. **WorkingCapitalErrorModal** 🪟
**Antes**: Modal com mensagem fixa
```vue
<farm-bodytext type="2" variation="regular">
  Não foi possível encontrar nenhuma informação cadastrada para seleção.
</farm-bodytext>
```

**Depois**: Modal dinâmico que aceita lista de mensagens
```vue
<div v-if="errorMessages && errorMessages.length > 0">
  <farm-bodytext
    v-for="(message, index) in errorMessages"
    :key="index"
    type="2"
    variation="regular"
    class="mb-2"
  >
    {{ message }}
  </farm-bodytext>
</div>
```

#### 2. **OperationForm.vue** 📝
- **Integração**: Conectado ao novo sistema de mensagens de erro
- **Watcher**: Monitora erros de campaigns automaticamente
- **Event Emitting**: Propaga mensagens de erro para componente pai

#### 3. **OperationCreate.vue** 🎛️
- **Estado de erro**: Gerencia `errorMessages` dinâmicamente
- **Propagação**: Recebe e passa mensagens para modais
- **Evento**: Handler `onError(messages)` atualizado

### ✅ Sistema de Tratamento de Erros

#### 1. **handlePreEligibilityError** ⚙️
**Melhoria**: Regex expandida para aceitar códigos alfanuméricos

**Antes**: Apenas códigos numéricos `(123)`
```typescript
const regex = /\(\d+\)$/;
```

**Depois**: Códigos alfanuméricos `(CM1)`, `(ABC123)`, `(F001)`
```typescript
const regex = /\([A-Za-z0-9]+\)$/;
```

#### 2. **Retry Policy Corrigido** 🔄
**Problema**: workingDays fazia 4 tentativas quando falhava (1 inicial + 3 retries)

**Solução**: Adicionado `retry: 0` para consistência com outras queries
```typescript
return useQuery({
  // ... outras configs
  retry: 0, // ← Adicionado
  // ... resto da config
});
```

### ✅ Arquitetura de Error Handling

```mermaid
flowchart TD
    A[API Call] --> B{Response Status}
    B -->|200 OK| C[Success - Clear Errors]
    B -->|400 Bad Request| D{Has Structured Errors?}
    B -->|Other Errors| E[Generic Error Handler]
    
    D -->|Yes| F[handlePreEligibilityError]
    D -->|No| E
    
    F --> G[Process Error Messages]
    E --> H[Default Error Message]
    
    G --> I[Display in ErrorModal]
    H --> I
    
    I --> J[User Sees Specific Messages]
```

---

## ✅ APIs Migradas para Modal de Erro

### 6. **Price Operation** 💵
- **Endpoint**: `POST /financing/{productId}/operation/{operationTypeId}/price/working-capital`
- **Implementação**: `useMutation` no `OperationForm.vue`
- **Erro atual**: ✅ Migrado para usar `handlePreEligibilityError` com modal estruturado
- **Localização**: `src/features/workingCapitalV2/components/OperationForm/OperationForm.vue`

### 7. **Working Capital List** 📋
- **Endpoint**: `GET /workingCapital/{productId}/operation/{operationId}`
- **Composable**: `useWorkingCapitalList`
- **Erro atual**: ✅ Migrado para usar mensagem específica "Erro ao buscar os dados da lista (código)"
- **Localização**: `src/features/workingCapitalV2/composables/useWorkingCapitalList/useWorkingCapitalList.ts`

## ❌ APIs Não Migradas (Não Abrem Modal)

**Nota**: As seguintes APIs fazem **tratamento silencioso** de erros e não precisam ser migradas:

### 1. **Commercial Products** 🏢
- **Endpoint**: `GET /api/v1/product/{productId}/commercial-product`
- **Composable**: `useFetchCommercialProducts`
- **Erro atual**: `onError: () => { commercialProducts.value = null; }`
- **Comportamento**: Falha silenciosa, sem modal

### 2. **Commercial Products Limits** 💰
- **Endpoint**: `GET /api/v1/product/{productId}/commercial-product/{commercialProductId}/limits`
- **Composable**: `useFetchCommercialProductsLimits`
- **Erro atual**: Similar ao anterior, apenas seta valores nulos
- **Comportamento**: Falha silenciosa, sem modal

### 3. **Originators** 🏛️
- **Endpoint**: `GET /workingCapital/{productId}/originators`
- **Query**: `query.originators.limitsByCommercialProduct`
- **Erro atual**: Não tem tratamento customizado, usa padrão do TanStack Query
- **Comportamento**: Falha silenciosa, sem modal

### 4. **Create Operation** ✅
- **Endpoint**: `POST /workingCapital/{productId}/operation/{operationId}`
- **Implementação**: `useMutation` no `OperationSimulationResult.vue`
- **Erro atual**: Usa `handlePreEligibilityError` diretamente no componente
- **Localização**: `src/features/workingCapitalV2/components/OperationSimulationResult/`

---

## 🎯 Benefícios Implementados

### Para os Usuários 👥
- **Clareza**: Sabem exatamente qual é o problema
- **Ação**: Podem tomar medidas específicas para resolver
- **Confiança**: Interface mais transparente e confiável
- **Produtividade**: Menos tempo perdido tentando entender erros

### Para Desenvolvedores 👨‍💻
- **Debugging**: Logs mais informativos para troubleshooting
- **Consistência**: Padrão unificado para tratamento de erros
- **Manutenibilidade**: Código mais organizado e extensível
- **Reusabilidade**: Helpers podem ser usados em outras features

### Para o Produto 📈
- **UX melhorada**: Experiência mais profissional
- **Suporte reduzido**: Menos tickets de dúvidas sobre erros
- **Conversão**: Usuários não abandonam por mensagens confusas

---

## 🚀 Próximos Passos

### Fase 1: Completar Migração APIs Restantes
1. **Migrar Commercial Products** - Implementar tratamento de erro estruturado
2. **Migrar Commercial Products Limits** - Adicionar mensagens específicas
3. **Migrar Originators** - Aplicar padrão consistente
4. **Centralizar Price/Create Operations** - Mover lógica para queries

### Fase 2: Melhorias UX
1. **Estados de Loading específicos** - Loading diferenciado por tipo de erro
2. **Retry inteligente** - Botão "Tentar novamente" contextual
3. **Feedback visual** - Indicadores visuais para diferentes tipos de erro

### Fase 3: Monitoramento
1. **Analytics de erros** - Tracking de tipos de erro mais comuns
2. **Alertas automáticos** - Notificação quando APIs falham muito
3. **Dashboard de saúde** - Visibilidade de performance das APIs

---

## 📚 Para Desenvolvedores

### Como Usar o Sistema

#### 1. **Para novas APIs** 
```typescript
// 1. Criar helper específico
export const handleNewApiError = (error: any): string[] => {
  if (error?.response?.status === 400 && error?.response?.data?.errors) {
    return handlePreEligibilityError(error);
  }
  return ['Mensagem genérica apropriada para esta API.'];
};

// 2. Usar na query configuration
return useQuery({
  queryKey,
  queryFn: async () => {
    try {
      const response = await newApiCall(request.value);
      if (callback) callback(response);
      return response;
    } catch (error) {
      if (onError) {
        const errorMessages = handleNewApiError(error);
        onError(errorMessages);
      }
      throw error;
    }
  },
  // ... outras configs
});
```

#### 2. **Para componentes**
```typescript
// No composable
const apiError = ref(null);

const handleApiError = (errorMessages: string[]) => {
  apiError.value = errorMessages;
};

// No componente Vue
const { apiError } = useMyComposable();

watch(apiError, (newError) => {
  if (newError && newError.length > 0) {
    emit('open-error-modal', newError);
  }
});
```

### Padrões Estabelecidos

1. **Sempre** usar `handlePreEligibilityError` para erros HTTP 400 estruturados
2. **Sempre** fornecer mensagem genérica para outros tipos de erro
3. **Sempre** adicionar `retry: 0` em queries que não devem fazer retry
4. **Sempre** limpar erros anteriores em caso de sucesso
5. **Sempre** emitir eventos para componentes pais quando apropriado

---

## 🔍 Exemplo Completo de Implementação

### Arquivo: `handleExampleError.ts`
```typescript
import { handlePreEligibilityError } from '@/features/workingCapitalV2/helpers/handlePreEligibilityError';

export const handleExampleError = (error: any): string[] => {
  // Verificar se é erro HTTP 400 com formato estruturado
  if (error?.response?.status === 400 && error?.response?.data?.errors) {
    return handlePreEligibilityError(error);
  }

  // Para outros erros, retornar mensagem genérica
  return ['Não foi possível carregar os dados do exemplo. Tente novamente.'];
};
```

### Arquivo: `queries/example/index.ts`
```typescript
import { handleExampleError } from './handleExampleError';

export default {
  getData({
    queryKey,
    request,
    options,
    callback,
    onError,
  }: {
    queryKey: any[];
    request?: ComputedRef<ExampleRequest>;
    options?: Partial<UseQueryOptions>;
    callback?: (data: any) => void;
    onError?: (errorMessages: string[]) => void;
  }) {
    return useQuery({
      queryKey,
      queryFn: async () => {
        try {
          const response = await getExampleData(request.value);
          if (callback) callback(response);
          return response;
        } catch (error) {
          if (onError) {
            const errorMessages = handleExampleError(error);
            onError(errorMessages);
          }
          throw error;
        }
      },
      retry: 0,
      refetchOnWindowFocus: false,
      ...(options as any),
    });
  },
};
```

### Arquivo: `composables/useExample.ts`
```typescript
export default function useExample() {
  const exampleError = ref(null);

  const handleExampleError = (errorMessages: string[]) => {
    exampleError.value = errorMessages;
  };

  const exampleQuery = query.example.getData({
    queryKey: key.example.getData(),
    request: exampleRequest,
    callback: (response) => {
      // Limpar erros anteriores em caso de sucesso
      exampleError.value = null;
    },
    onError: handleExampleError,
    options: {
      enabled: computed(() => !!someCondition.value),
    },
  });

  return {
    exampleError,
    // ... outros retornos
  };
}
```

---

## 📊 Resumo das Mudanças

| Componente | Status | Melhoria |
|------------|--------|----------|
| **WorkingCapitalErrorModal** | ✅ Implementado | Mensagens dinâmicas |
| **Campaigns API** | ✅ Implementado | Erros estruturados |
| **Suppliers API** | ✅ Implementado | Erros estruturados |
| **Bank Accounts API** | ✅ Implementado | Erros estruturados |
| **Working Days API** | ✅ Implementado | Erros estruturados + retry fix |
| **Due Dates API** | ✅ Implementado | Erros estruturados |
| **Price Operation** | ✅ Implementado | Erros estruturados com modal |
| **Working Capital List** | ✅ Implementado | Erros estruturados com notification |
| **Commercial Products API** | ❌ Não Migrado | Falha silenciosa (sem modal) |
| **Limits API** | ❌ Não Migrado | Falha silenciosa (sem modal) |
| **Originators API** | ❌ Não Migrado | Falha silenciosa (sem modal) |
| **Create Operation** | ❌ Não Migrado | Já usa handlePreEligibilityError |

---

## 🏁 Conclusão

A implementação do novo sistema de mensagens de erro foi **CONCLUÍDA COM SUCESSO** para todas as APIs que abrem modais de erro na feature Working Capital V2.

### ✅ **Migração Completa Realizada**
- **8 APIs migradas** para o novo sistema de tratamento de erro HTTP 400 estruturado
- **100% das APIs que abrem modais** agora usam mensagens genéricas com códigos específicos
- **Tratamento especial** para Working Capital List usando notification ao invés de modal
- **Padrão consistente** estabelecido para futuras implementações

### 🎯 **Objetivo Alcançado**
- ✅ Tratamento de chamadas ao backend com resposta 400
- ✅ Modal genérico com erro entre parênteses
- ✅ Mensagem padronizada: "Não foi possível encontrar nenhuma informação cadastrada para seleção (código)"
- ✅ Exceção para lista: "Erro ao buscar os dados da lista (código)"

### 📋 **APIs Não Migradas (Por Design)**
As APIs não migradas fazem **tratamento silencioso** de erros (não abrem modais):
- Commercial Products
- Commercial Products Limits  
- Originators
- Create Operation (já usa handlePreEligibilityError)

### 🔧 **Implementações Técnicas Realizadas**
1. **handlePreEligibilityError** - Atualizado para mensagem genérica
2. **WorkingCapitalErrorModal** - Suporte a mensagens dinâmicas
3. **Query configurations** - Tratamento de erro HTTP 400 estruturado
4. **Composables** - Integração com sistema de erro
5. **Watchers automáticos** - Detecção e exibição de erros

**Impacto realizado:**
- 📈 **Mensagens de erro mais claras** para usuários
- 📈 **Experiência consistente** em toda a aplicação
- 📈 **Código mais maintível** para desenvolvedores
- 📈 **Padrão estabelecido** para futuras features

---

*Documento atualizado em: $(date)*  
*Versão: 2.0 - Implementação Completa*  
*Status: ✅ CONCLUÍDO*