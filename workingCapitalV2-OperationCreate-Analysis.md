# WorkingCapitalV2 OperationCreate - Technical Analysis

## Overview

The OperationCreate feature handles working capital operation creation with two types:
- **Origination** (ID: 2) - Standard operations
- **Bridge Credit** (ID: 3) - Operations with receivables amortization

## Component Architecture

```mermaid
graph TD
    A[OperationCreate.vue] --> B[OperationForm.vue]
    A --> C[OperationSimulationResult.vue]
    A --> D[Modals]
    
    B --> E[Form Fields]
    C --> F[Results Display]
    D --> G[Product/Supplier/Error Modals]
```

### Main Components

| Component | Responsibility |
|-----------|----------------|
| **OperationCreate** | Container, modal management, state coordination |
| **OperationForm** | Form logic, validation, progressive field enabling |
| **OperationSimulationResult** | Results display, operation creation |

## Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Form
    participant API
    participant Store
    
    User->>Form: Mount Component
    Form->>API: Load Products & Limits
    API-->>Store: Save wallet data
    
    User->>Form: Select Product
    Form->>API: Get Campaigns & Originators
    
    User->>Form: Fill Values
    Form->>API: Price Operation
    API-->>Form: Get Simulation ID
    
    Form->>API: Get Suppliers
    User->>Form: Select Supplier
    Form->>API: Get Bank Accounts
    
    User->>Form: Submit
    Form->>API: Create Operation
```

## API Integration

### Key Endpoints

1. **Commercial Products**
   - Endpoint: `GET /api/v1/product/{productId}/commercial-product`
   - Trigger: Component mount
   - Purpose: Load available products

2. **Campaigns**
   - Endpoint: `GET /workingCapital/{productId}/operation/{operationId}/commercial_product_id/{commercialProductId}/campaigns`
   - Trigger: Product selection
   - Purpose: Load campaigns for product

3. **Working Days**
   - Endpoint: `GET /receivables/orders/v1/products/{productId}/supersection/workingdays`
   - Trigger: Campaign selection
   - Purpose: Get transfer dates

4. **Price Operation**
   - Endpoint: `POST /financing/{productId}/operation/{operationTypeId}/price/working-capital`
   - Trigger: All form fields completed
   - Purpose: Calculate pricing, get simulation ID

5. **Suppliers**
   - Endpoint: `GET /workingCapital/{simulationId}/providers`
   - Trigger: After pricing
   - Purpose: Get eligible suppliers

6. **Create Operation**
   - Endpoint: `POST /workingCapital/{productId}/operation/{operationId}`
   - Trigger: User confirmation
   - Purpose: Final operation creation

## State Management

### Vuex Store (dashboards module)
```typescript
{
  wallet: [],           // Products with limits
  currentLimit: null,   // Selected product
  isWalletError: false, // Error state
  modalityType: null    // Operation type
}
```

### Form State (useWorkingCapitalCreateForm)
```typescript
{
  originatorId: null,
  campaignId: null,
  value: '',
  supplierId: null,
  supplierTransferDate: null,
  supplierBankAccount: null,
  dueDate: null
}
```

## Key Composables

### useCommercialProducts
- **Purpose**: Load and combine products with limits
- **Dependencies**: API calls for products and limits
- **Output**: Combined wallet data to store

### useCampaigns
- **Purpose**: Manage campaign selection
- **Trigger**: When product is selected
- **Auto-selection**: Single campaign auto-selected

### useSuppliers
- **Purpose**: Handle supplier and bank account selection
- **Dependency**: Requires simulationId from pricing
- **Auto-selection**: Single supplier and first bank account

### useWorkingCapitalCreateForm
- **Purpose**: Central form state and validation
- **Features**: Progressive validation, computed rules
- **Integration**: Shared across all components

## Form Validation Flow

```mermaid
flowchart TD
    A[User Input] --> B{Field Valid?}
    B -->|Yes| C[Enable Next Field]
    B -->|No| D[Show Error]
    C --> E{All Fields Complete?}
    E -->|Yes| F[Enable Submit]
    E -->|No| G[Continue Filling]
    D --> A
```

### Validation Rules
- **Required fields**: All form fields mandatory
- **Value validation**: Must be > 0 and <= available limit
- **Date validation**: Must be within allowed range
- **Progressive enabling**: Fields enabled based on previous selections

## Modal System

### Modal Types and Triggers

| Modal | Trigger | Purpose |
|-------|---------|---------|
| **ProductsModal** | No products/multiple options | Product selection |
| **LimitsModal** | Edit limit button | Originator selection |
| **SupplierModal** | Edit supplier button | Supplier selection |
| **BankAccountModal** | Edit account button | Bank account selection |
| **ErrorModal** | API errors/validation failures | Error display |
| **SuccessModal** | Pre-eligibility approved | Operation confirmation |

## Error Handling

### Error Types
- **API Errors**: Network failures, server errors
- **Validation Errors**: Form field validation failures
- **Business Logic Errors**: No available dates, insufficient limits
- **Data Loading Errors**: Products/limits loading failures

### Recovery Patterns
- **Error Modals**: User acknowledgment and retry
- **Contact Card**: Manual support for data loading issues
- **Field Reset**: Clear dependent fields on errors
- **Graceful Degradation**: Fallback to empty states

## Performance Optimizations

### React Query Configuration
```typescript
// Dynamic data - no cache
{ cacheTime: 0, retry: 0 }

// Semi-static data - short cache
{ staleTime: 60000, keepPreviousData: true }

// Conditional execution
{ enabled: computed(() => !!dependency.value) }
```

### Lazy Loading
- Modal components loaded asynchronously
- Heavy components split with `defineAsyncComponent`

## Testing Strategy

### Unit Tests
- Composable state management
- Form validation logic
- Data transformation functions

### Integration Tests
- Complete form flow
- API error scenarios
- Modal interactions

### E2E Tests
- Happy path: Complete operation creation
- Error recovery flows
- Auto-selection scenarios

## Key Implementation Notes

### Progressive Form Logic
1. Load products → Enable product selection
2. Select product → Load campaigns & originators
3. Select campaign → Load working days
4. Select dates → Enable pricing
5. Price operation → Enable supplier selection
6. Select supplier → Load bank accounts
7. Complete form → Enable submission

### Auto-Selection Rules
- Single product: Auto-selected if available limit > 0
- Single campaign: Auto-selected
- Single originator: Auto-selected
- Single supplier: Auto-selected
- First bank account: Auto-selected

### State Synchronization
- Store changes trigger composable updates
- Form field changes reset dependent fields
- API errors trigger appropriate error states
- Loading states coordinate across components

## Conclusion

The OperationCreate feature demonstrates:
- **Complex State Orchestration**: Multi-layered state coordination
- **Progressive UX**: Conditional field enabling based on data
- **Robust Error Handling**: Comprehensive error recovery
- **Performance Optimization**: Strategic caching and lazy loading
- **Modular Architecture**: Reusable composables and components

This architecture provides a maintainable foundation for complex financial operations while ensuring optimal user experience.
